<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title><?= isset($title) ? $title . ' - ' : '' ?>PCOLLX Field Portal</title>
    <link rel="icon" type="image/x-icon" href="<?= base_url('assets/system_images/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= base_url('assets/system_images/dakoii-logo-icon.png') ?>">


    <!-- Minimal CSS for low bandwidth -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            -webkit-tap-highlight-color: transparent;
        }

        .container {
            max-width: 100%;
            padding: 0 15px;
            margin: 0 auto;
        }

        /* Header */
        .header {
            background: #007bff;
            color: white;
            padding: 12px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 18px;
            font-weight: 600;
        }

        .user-info {
            font-size: 14px;
            text-align: right;
        }

        /* Main content */
        .main {
            padding: 20px 0;
            min-height: calc(100vh - 140px);
        }

        /* Button styles */
        .btn {
            display: inline-block;
            padding: 12px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: background 0.2s;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn:hover, .btn:focus {
            background: #0056b3;
            color: white;
            text-decoration: none;
        }

        .btn-block {
            display: block;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* Grid system */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -7.5px;
        }

        .col {
            flex: 1;
            padding: 0 7.5px;
            margin-bottom: 15px;
        }

        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        /* Stats */
        .stat-card {
            text-align: center;
            padding: 15px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        /* Forms */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }

        /* Alerts */
        .alert {
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Footer */
        .footer {
            background: #f8f9fa;
            padding: 15px 0;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
        }

        /* Responsive */
        @media (max-width: 576px) {
            .container {
                padding: 0 10px;
            }

            .header-content {
                flex-direction: column;
                gap: 5px;
            }

            .user-info {
                text-align: center;
            }

            .col-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Touch improvements */
        .btn, .form-control {
            -webkit-appearance: none;
            -webkit-border-radius: 0;
        }

        /* Prevent zoom on input focus */
        @media screen and (max-width: 767px) {
            .form-control {
                font-size: 16px;
            }
        }
    </style>

        <!-- Minimalist modern overrides -->
        <style>
            :root {
                --bg: #f6f7fb;
                --text: #1f2937;
                --muted: #6b7280;
                --primary: #0d6efd;
                --primary-dark: #0b5ed7;
                --success: #16a34a;
                --danger: #dc2626;
                --secondary: #6c757d;
                --card-bg: #ffffff;
                --border: #e5e7eb;
            }

            body { background: var(--bg); color: var(--text); }

            .header { background: var(--primary); }
            .btn { border-radius: 10px; box-shadow: 0 1px 2px rgba(0,0,0,0.06); }
            .btn:focus { outline: 2px solid rgba(13,110,253,0.25); outline-offset: 2px; }
            .btn-sm { padding: 8px 12px; min-height: 36px; font-size: 14px; }

            .card { border: 1px solid var(--border); border-radius: 10px; box-shadow: 0 1px 3px rgba(0,0,0,0.06); }
            .card-title { margin-bottom: 10px; color: #0f172a; }

            .text-muted { color: var(--muted); }

            .form-control { border: 1.5px solid var(--border); border-radius: 10px; }
            .form-control:focus { border-color: var(--primary); box-shadow: 0 0 0 3px rgba(13,110,253,0.15); }

            .table-responsive { width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch; }
            .table { width: 100%; border-collapse: separate; border-spacing: 0; background: var(--card-bg); }
            .table th, .table td { padding: 12px 14px; vertical-align: middle; }
            .table thead.table-dark th { background: #0f172a; color: #fff; border-bottom: 1px solid #0b1220; }
            .table tbody.table-light td { background: #fff; border-top: 1px solid var(--border); }
            .table-hover tbody tr:hover { background: #f3f4f6; }

            .badge { display: inline-block; padding: 4px 10px; border-radius: 9999px; font-size: 12px; color: #fff; }

            .alert { border: 1px solid transparent; }
            .alert-success { border-color: #b7e4c7; }
            .alert-danger { border-color: #f5c2c7; }

            /* Subtle footer tone */
            .footer { background: #f3f4f6; color: var(--muted); }

            /* Modal styles */
            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .modal-content {
                background: white;
                border-radius: 10px;
                width: 100%;
                max-width: 500px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            }

            .modal-header {
                padding: 20px 20px 0 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid var(--border);
                margin-bottom: 20px;
            }

            .modal-header h3 {
                margin: 0;
                color: var(--text);
                font-size: 18px;
                font-weight: 600;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: var(--muted);
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.2s;
            }

            .modal-close:hover {
                background: #f3f4f6;
                color: var(--text);
            }

            .modal-body {
                padding: 0 20px;
            }

            .modal-footer {
                padding: 20px;
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                border-top: 1px solid var(--border);
                margin-top: 20px;
            }

            /* Mobile modal adjustments */
            @media (max-width: 576px) {
                .modal {
                    padding: 10px;
                }

                .modal-content {
                    max-height: 95vh;
                }

                .modal-header, .modal-body, .modal-footer {
                    padding-left: 15px;
                    padding-right: 15px;
                }

                .modal-footer {
                    flex-direction: column;
                }

                .modal-footer .btn {
                    width: 100%;
                    margin-bottom: 10px;
                }

                .modal-footer .btn:last-child {
                    margin-bottom: 0;
                }
            }
        </style>

</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">PCOLLX Field</div>
                <?php if (isset($user)): ?>
                <div class="user-info">
                    <div><?= esc($user['name']) ?></div>
                    <div style="font-size: 12px; opacity: 0.9;"><?= esc($user['role']) ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div>&copy; <?= date('Y') ?> PCOLLX Field Portal - ICCC Papua New Guinea</div>
        </div>
    </footer>

    <!-- Minimal JavaScript for basic functionality -->
    <script>
        // Simple form loading states
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                        if (submitBtn.textContent) {
                            submitBtn.dataset.originalText = submitBtn.textContent;
                            submitBtn.textContent = 'Loading...';
                        }
                    }
                });
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);

        // Modal functions
        function openCreateModal(itemId, itemName) {
            document.getElementById('createItemId').value = itemId;
            document.getElementById('createModalTitle').textContent = 'Enter Price Data - ' + itemName;
            document.getElementById('createModal').style.display = 'flex';
            // Focus on price input
            setTimeout(() => {
                const priceInput = document.querySelector('#createModal input[name="price"]');
                if (priceInput) priceInput.focus();
            }, 100);
        }

        function openEditModal(entryId, itemName, price, remarks) {
            document.getElementById('editModalTitle').textContent = 'Edit Price Data - ' + itemName;
            document.getElementById('editPrice').value = price;
            document.getElementById('editRemarks').value = remarks;
            document.getElementById('editForm').action = '<?= base_url('field/collect/update/') ?>' + entryId;
            document.getElementById('editModal').style.display = 'flex';
            // Focus on price input
            setTimeout(() => {
                const priceInput = document.getElementById('editPrice');
                if (priceInput) priceInput.focus();
            }, 100);
        }

        // Event listeners for modal buttons
        document.addEventListener('click', function(event) {
            // Handle create button clicks
            if (event.target.classList.contains('create-btn')) {
                const itemId = event.target.getAttribute('data-item-id');
                const itemName = event.target.getAttribute('data-item-name');
                openCreateModal(itemId, itemName);
            }

            // Handle edit button clicks
            if (event.target.classList.contains('edit-btn')) {
                const entryId = event.target.getAttribute('data-entry-id');
                const itemName = event.target.getAttribute('data-item-name');
                const price = event.target.getAttribute('data-price');
                const remarks = event.target.getAttribute('data-remarks');
                openEditModal(entryId, itemName, price, remarks);
            }
        });

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // Reset forms
            const forms = document.querySelectorAll('#' + modalId + ' form');
            forms.forEach(form => form.reset());
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                const modalId = event.target.id;
                if (modalId) {
                    closeModal(modalId);
                }
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const visibleModals = document.querySelectorAll('.modal[style*="flex"]');
                visibleModals.forEach(modal => {
                    closeModal(modal.id);
                });
            }
        });
    </script>
</body>
</html>
