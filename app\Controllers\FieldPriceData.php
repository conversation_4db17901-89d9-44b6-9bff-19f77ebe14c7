<?php

namespace App\Controllers;

use App\Models\ActivityModel;
use App\Models\ActivityBusinessLocationModel;
use App\Models\ActivityPriceCollectionDataModel;
use App\Models\BusinessLocationModel;
use App\Models\PriceDataModel;

class FieldPriceData extends BaseController
{
    protected $activityModel;
    protected $activityLocationModel;
    protected $priceCollectionModel;
    protected $businessLocationModel;
    protected $priceDataModel;

    public function __construct()
    {
        $this->activityModel = new ActivityModel();
        $this->activityLocationModel = new ActivityBusinessLocationModel();
        $this->priceCollectionModel = new ActivityPriceCollectionDataModel();
        $this->businessLocationModel = new BusinessLocationModel();
        $this->priceDataModel = new PriceDataModel();
    }

    // GET: field/collect
    public function index()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');

        $activities = $this->activityModel
            ->where('org_id', $orgId)
            ->where('is_deleted', false)
            ->orderBy('date_from', 'DESC')
            ->findAll();

        // Enrich with assignment counts (locations & users)
        $activityUserModel = new \App\Models\ActivityUserModel();
        foreach ($activities as &$act) {
            $actId = (int)($act['id'] ?? 0);
            $act['location_count'] = $this->activityLocationModel->getAssignmentStatsByActivity($actId);
            $act['user_count'] = $activityUserModel->where('activity_id', $actId)->countAllResults();
        }
        unset($act);

        $data = [
            'title' => 'Activities - Price Collection',
            'activities' => $activities,
        ];

        return view('field_price_data/field_price_data_list', $data);
    }

    // GET: field/collect/activity/{id}
    public function activity(int $activityId)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');

        $activity = $this->activityModel
            ->where('id', $activityId)
            ->where('org_id', $orgId)
            ->where('is_deleted', false)
            ->first();
        if (!$activity) {
            return redirect()->to('field/collect')->with('error', 'Activity not found.');
        }

        $locations = $this->activityLocationModel->getLocationsByActivity($activityId);

        $data = [
            'title' => 'Business Locations - ' . $activity['activity_name'],
            'activity' => $activity,
            'locations' => $locations,
        ];

        return view('field_price_data/field_price_data_activity', $data);
    }

    // GET: field/collect/activity/{activityId}/location/{locationId}
    public function location(int $activityId, int $businessLocationId)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        $orgId = (int) session()->get('field_org_id');

        $activity = $this->activityModel
            ->where('id', $activityId)
            ->where('org_id', $orgId)
            ->where('is_deleted', false)
            ->first();
        if (!$activity) {
            return redirect()->to('field/collect')->with('error', 'Activity not found.');
        }

        $location = $this->businessLocationModel
            ->where('id', $businessLocationId)
            ->where('is_deleted', false)
            ->first();
        if (!$location) {
            return redirect()->to('field/collect/activity/' . $activityId)->with('error', 'Business location not found.');
        }

        // Verify assignment (optional safety)
        if (!$this->activityLocationModel->isLocationAssignedToActivity($activityId, $businessLocationId)) {
            return redirect()->to('field/collect/activity/' . $activityId)->with('error', 'Location is not assigned to this activity.');
        }

        // Existing price entries for this combination
        $entries = $this->priceCollectionModel
            ->select('activity_price_collection_data.*, goods_items.item as item_name')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->where('activity_price_collection_data.activity_id', $activityId)
            ->where('activity_price_collection_data.business_location_id', $businessLocationId)
            ->where('activity_price_collection_data.is_deleted', false)
            ->orderBy('goods_items.item', 'ASC')
            ->findAll();
        // Map entries by item for quick lookup to enforce one entry per item per location in UI
        $entriesByItem = [];
        foreach ($entries as $en) {
            if (isset($en['item_id'])) {
                $entriesByItem[$en['item_id']] = $en;
            }
        }

        // Items list with brand type (primary/substitute) for quick scrolling
        $db = \Config\Database::connect();
        $items = $db->table('goods_items gi')
            ->select('gi.id, gi.item, gb.type as brand_type')
            ->join('goods_brands gb', 'gb.id = gi.goods_brand_id', 'left')
            ->where('gi.is_deleted', false)
            ->where('gi.status', 'active')
            ->orderBy('gb.type', 'ASC')
            ->orderBy('gi.item', 'ASC')
            ->get()->getResultArray();

        $data = [
            'title' => 'Collect Prices - ' . $location['business_name'],
            'activity' => $activity,
            'location' => $location,
            'entries' => $entries,
            'entriesByItem' => $entriesByItem,
            'items' => $items,
        ];

        return view('field_price_data/field_price_data_location', $data);
    }

    // POST: field/collect/create
    public function create()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/collect');
        }

        $orgId = (int) session()->get('field_org_id');
        $userId = (int) session()->get('field_user_id');

        $activityId = (int) $this->request->getPost('activity_id');
        $businessLocationId = (int) $this->request->getPost('business_location_id');
        $itemId = (int) $this->request->getPost('item_id');
        $price = $this->request->getPost('price');
        $remarks = $this->request->getPost('remarks');

        $redirect = 'field/collect/activity/' . $activityId . '/location/' . $businessLocationId;

        $rules = [
            'activity_id' => 'required|is_natural_no_zero',
            'business_location_id' => 'required|is_natural_no_zero',
            'item_id' => 'required|is_natural_no_zero',
            'price' => 'required|decimal|greater_than[0]',
            'remarks' => 'permit_empty|max_length[65535]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->to($redirect)->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'org_id' => $orgId,
            'business_location_id' => $businessLocationId,
            'activity_id' => $activityId,
            'user_id' => $userId,
            'item_id' => $itemId,
            'price' => $price,
            'remarks' => $remarks,
            'status' => 'active',
            'created_by' => $userId,
        ];

        try {
            $this->priceCollectionModel->insert($data);
            return redirect()->to($redirect)->with('success', 'Price entry added successfully.');
        } catch (\Throwable $e) {
            return redirect()->to($redirect)->withInput()->with('error', $e->getMessage());
        }
    }

    // GET: field/collect/edit/{id}
    public function edit(int $id)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $entry = $this->priceCollectionModel
            ->select('activity_price_collection_data.*, goods_items.item as item_name, business_locations.business_name, activities.activity_name')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
            ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
            ->where('activity_price_collection_data.id', $id)
            ->where('activity_price_collection_data.is_deleted', false)
            ->first();

        if (!$entry) {
            return redirect()->to('field/collect')->with('error', 'Price entry not found.');
        }

        $data = [
            'title' => 'Edit Price Entry',
            'entry' => $entry,
        ];

        return view('field_price_data/field_price_data_edit', $data);
    }

    // POST: field/collect/update/{id}
    public function update(int $id)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/collect');
        }

        $entry = $this->priceCollectionModel->find($id);
        if (!$entry || ($entry['is_deleted'] ?? false)) {
            return redirect()->to('field/collect')->with('error', 'Price entry not found.');
        }

        $rules = [
            'price' => 'required|decimal|greater_than[0]',
            'remarks' => 'permit_empty|max_length[65535]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userId = (int) session()->get('field_user_id');

        $data = [
            'price' => $this->request->getPost('price'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $userId,
        ];

        try {
            $this->priceCollectionModel->update($id, $data);
            $redirect = 'field/collect/activity/' . $entry['activity_id'] . '/location/' . $entry['business_location_id'];
            return redirect()->to($redirect)->with('success', 'Price entry updated successfully.');
        } catch (\Throwable $e) {
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }

    // POST: field/collect/delete/{id}
    public function delete(int $id)
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/collect');
        }

        $entry = $this->priceCollectionModel->find($id);
        if (!$entry || ($entry['is_deleted'] ?? false)) {
            return redirect()->to('field/collect')->with('error', 'Price entry not found.');
        }

        $userId = (int) session()->get('field_user_id');
        // Soft delete aligned with existing patterns (is_deleted flag)
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => $userId,
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        $this->priceCollectionModel->update($id, $deleteData);

        $redirect = 'field/collect/activity/' . $entry['activity_id'] . '/location/' . $entry['business_location_id'];
        return redirect()->to($redirect)->with('success', 'Price entry deleted successfully.');
    }
}

