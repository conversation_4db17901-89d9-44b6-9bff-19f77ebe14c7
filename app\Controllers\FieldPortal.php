<?php

namespace App\Controllers;

use App\Models\UserModel;

class FieldPortal extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    /**
     * Display field portal dashboard
     */
    public function index()
    {
        // Check if already logged in
        if (session()->get('field_logged_in')) {
            return redirect()->to('field/dashboard');
        }

        // If coming from admin login, check if user should be redirected to field portal
        if (session()->get('admin_logged_in')) {
            $isAdmin = session()->get('admin_is_admin');
            $isSupervisor = session()->get('admin_is_supervisor');
            
            // If user is not admin or supervisor, redirect to field portal
            if (!$isAdmin && !$isSupervisor) {
                // Transfer session data to field portal
                $sessionData = [
                    'field_user_id' => session()->get('admin_user_id'),
                    'field_email' => session()->get('admin_email'),
                    'field_name' => session()->get('admin_name'),
                    'field_role' => session()->get('admin_role'),
                    'field_org_id' => session()->get('admin_org_id'),
                    'field_logged_in' => true
                ];

                // Clear admin session
                $adminSessionKeys = [
                    'admin_user_id', 'admin_email', 'admin_name', 'admin_role',
                    'admin_is_admin', 'admin_is_supervisor', 'admin_org_id', 'admin_logged_in'
                ];
                foreach ($adminSessionKeys as $key) {
                    session()->remove($key);
                }

                // Set field session
                session()->set($sessionData);
                
                return redirect()->to('field/dashboard');
            }
        }

        return redirect()->to('admin');
    }

    /**
     * Display field dashboard
     */
    public function dashboard()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $userId = session()->get('field_user_id');

        // Build task stats for dashboard (lightweight, no extra libs)
        $activityUserModel = new \App\Models\ActivityUserModel();
        $assigned = $activityUserModel
            ->select('activities.id as activity_id, activities.activity_name, activities.status, activities.status_at')
            ->join('activities', 'activities.id = activity_users.activity_id', 'left')
            ->where('activity_users.user_id', $userId)
            ->where('activities.is_deleted', false)
            ->orderBy('activities.status_at', 'DESC')
            ->findAll();

        $total = count($assigned);
        $pending = 0; $completed = 0; $completedToday = 0;
        $today = date('Y-m-d');
        $recent = [];
        foreach ($assigned as $row) {
            $status = $row['status'] ?? '';
            $name = $row['activity_name'] ?? 'Task';
            $statusAt = isset($row['status_at']) ? (string)$row['status_at'] : null;
            $statusDate = $statusAt ? substr($statusAt, 0, 10) : null;

            if ($status === 'submitted' || $status === 'approved') {
                $completed++;
                if ($statusDate === $today) { $completedToday++; }
                $recent[] = [
                    'label' => '✅ Completed: ' . $name,
                    'time' => $statusAt ? date('M j, Y H:i', strtotime($statusAt)) : '',
                ];
            } else {
                $pending++;
                if ($statusAt) {
                    $recent[] = [
                        'label' => '📝 Active: ' . $name,
                        'time' => date('M j, Y H:i', strtotime($statusAt)),
                    ];
                }
            }
        }
        // Limit recent activity to latest 5
        $recent = array_slice($recent, 0, 5);

        $data = [
            'title' => 'Field Dashboard',
            'user' => [
                'id' => $userId,
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role'),
                'is_admin' => (int) session()->get('admin_is_admin') === 1 || (int) session()->get('field_is_admin') === 1 ? 1 : 0,
                'is_supervisor' => (int) session()->get('admin_is_supervisor') === 1 || (int) session()->get('field_is_supervisor') === 1 ? 1 : 0,
            ],
            'stats' => [
                'total' => $total,
                'pending' => $pending,
                'completed' => $completed,
                'completed_today' => $completedToday,
            ],
            'recent_activity' => $recent,
        ];

        return view('field_portal/field_dashboard', $data);
    }



    /**
     * Display price collection form
     */
    public function collect()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'Collect Prices',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_collect', $data);
    }

    /**
     * Display reports
     */
    public function reports()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $data = [
            'title' => 'My Reports',
            'user' => [
                'id' => session()->get('field_user_id'),
                'name' => session()->get('field_name'),
                'email' => session()->get('field_email'),
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_reports', $data);
    }

    /**
     * Display profile
     */
    public function profile()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }

        $userId = session()->get('field_user_id');
        $dbUser = $this->userModel->find($userId);
        if (!$dbUser) {
            return redirect()->to('field/dashboard')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'My Profile',
            'user' => [
                'id' => $userId,
                'name' => $dbUser['name'] ?? session()->get('field_name'),
                'email' => $dbUser['email'] ?? session()->get('field_email'),
                'phone' => $dbUser['phone'] ?? '',
                'role' => session()->get('field_role')
            ]
        ];

        return view('field_portal/field_profile', $data);
    }

    // POST: field/profile/update
    public function updateProfile()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/profile');
        }

        $userId = session()->get('field_user_id');
        $user = $this->userModel->find($userId);
        if (!$user) {
            return redirect()->to('field/dashboard')->with('error', 'User not found.');
        }

        $rules = [
            'name' => 'required|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$userId}]",
            'phone' => 'permit_empty|max_length[200]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'updated_by' => $userId,
        ];

        $this->userModel->skipValidation(true);
        $result = $this->userModel->update($userId, $updateData);
        $this->userModel->skipValidation(false);

        if ($result) {
            session()->set([
                'field_name' => $updateData['name'],
                'field_email' => $updateData['email'],
            ]);
            return redirect()->to('field/profile')->with('success', 'Profile updated successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update profile.');
    }

    // POST: field/profile/change-password
    public function changePassword()
    {
        if (!session()->get('field_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the field portal.');
        }
        if (!$this->request->is('post')) {
            return redirect()->to('field/profile');
        }

        $userId = session()->get('field_user_id');
        $user = $this->userModel->find($userId);
        if (!$user) {
            return redirect()->to('field/dashboard')->with('error', 'User not found.');
        }

        $currentPassword = (string)$this->request->getPost('current_password');
        $newPassword = (string)$this->request->getPost('new_password');
        $confirmPassword = (string)$this->request->getPost('confirm_password');

        $errors = [];
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $errors[] = 'All password fields are required.';
        }
        if (!password_verify($currentPassword, (string)$user['password'])) {
            $errors[] = 'Current password is incorrect.';
        }
        if (strlen($newPassword) < 4) {
            $errors[] = 'New password must be at least 4 characters long.';
        }
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'New password and confirmation do not match.';
        }

        if (!empty($errors)) {
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        $this->userModel->skipValidation(true);
        $updated = $this->userModel->update($userId, [
            'password' => $newPassword,
            'updated_by' => $userId,
        ]);
        $this->userModel->skipValidation(false);

        if ($updated) {
            return redirect()->to('field/profile')->with('success', 'Password changed successfully.');
        }

        return redirect()->back()->with('error', 'Failed to change password.');
    }

    /**
     * Logout field user
     */
    public function logout()
    {
        // Remove field session data
        $fieldSessionKeys = [
            'field_user_id',
            'field_email', 
            'field_name',
            'field_role',
            'field_org_id',
            'field_logged_in'
        ];

        foreach ($fieldSessionKeys as $key) {
            session()->remove($key);
        }

        return redirect()->to('admin')->with('success', 'You have been logged out successfully.');
    }



    /**
     * API endpoint to submit price data (for mobile optimization)
     */
    public function apiSubmitPrice()
    {
        // Check if logged in
        if (!session()->get('field_logged_in')) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        // Check if request method is POST
        if (!$this->request->is('post')) {
            return $this->response->setJSON(['error' => 'Invalid request method'])->setStatusCode(405);
        }

        // Get JSON data
        $data = $this->request->getJSON(true);

        // Basic validation
        if (empty($data['task_id']) || empty($data['price']) || empty($data['goods_item'])) {
            return $this->response->setJSON(['error' => 'Missing required fields'])->setStatusCode(400);
        }

        // Mock successful submission - will be replaced with actual database operations
        $response = [
            'success' => true,
            'message' => 'Price data submitted successfully',
            'submission_id' => rand(1000, 9999),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $this->response->setJSON($response);
    }
}
