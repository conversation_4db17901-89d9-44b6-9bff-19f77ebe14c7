<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>
<div class="card">
    <h1 class="card-title">✏️ Edit Price Entry</h1>
    <p style="color:#666;margin-bottom:0;">
        Activity: <strong><?= esc($entry['activity_name']) ?></strong><br>
        Location: <strong><?= esc($entry['business_name']) ?></strong><br>
        Item: <strong><?= esc($entry['item_name']) ?></strong>
    </p>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger"><?= session()->getFlashdata('error') ?></div>
<?php endif; ?>
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <?php foreach (session()->getFlashdata('errors') as $err): ?>
            <div>• <?= esc($err) ?></div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<div class="card">
    <form method="post" action="<?= base_url('field/collect/update/' . $entry['id']) ?>">
        <div class="form-group">
            <label class="form-label">Price</label>
            <input type="number" name="price" step="0.01" min="0" class="form-control" value="<?= esc($entry['price']) ?>" required>
        </div>
        <div class="form-group">
            <label class="form-label">Remarks</label>
            <textarea name="remarks" rows="2" class="form-control" placeholder="Optional notes..."><?= esc($entry['remarks'] ?? '') ?></textarea>
        </div>

        <div style="display:flex;gap:10px;">
            <button type="submit" class="btn btn-success">Update</button>
            <a href="<?= base_url('field/collect/activity/' . $entry['activity_id'] . '/location/' . $entry['business_location_id']) ?>" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

